generator client {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["cron"]
}

model city {
  id        String   @id
  name      String
  stateId   String
  createdAt DateTime @default(now())
  state     state    @relation(fields: [stateId], references: [id], onDelete: Cascade)

  @@unique([name, stateId])
  @@index([stateId])
  @@schema("cron")
}

model company {
  id                      String        @id
  name                    String        @unique
  domain                  String?       @unique
  website                 String?
  logoUrl                 String?
  overview                String?
  social                  Json?
  headquartersCity        String?
  headquarters_state_id   String?
  headquarters_country_id String?
  companySize             CompanySize?
  companyStage            CompanyStage?
  founded                 Int?
  jobCount                Int?
  activeJobCount          Int?
  aiRatingScore           Float?
  aiAnalystNote           Json?
  createdAt               DateTime      @default(now())
  country                 country?      @relation(fields: [headquarters_country_id], references: [id])
  state                   state?        @relation(fields: [headquarters_state_id], references: [id])
  job_listing             job_listing[]

  @@schema("cron")
}

model country {
  id        String    @id
  name      String    @unique
  isoCode   String?   @unique
  createdAt DateTime  @default(now())
  company   company[]
  school    school[]
  state     state[]

  @@schema("cron")
}

model job_collections {
  id        String   @id
  name      String   @unique
  slug      String   @unique
  platform  String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model job_listing {
  id                     String             @id
  platform               String
  jobId                  String
  title                  String
  company                String
  location               String
  url                    String             @unique
  isActive               Boolean            @default(true)
  isProcessing           Boolean            @default(false)
  createdAt              DateTime           @default(now())
  lastCheckedAt          DateTime
  employmentType         String?
  remoteType             String?
  experienceLevel        String?
  description            String?
  postedDate             DateTime?
  closedAt               DateTime?
  applyLink              String?
  benefits               String[]           @default([])
  requirements           String[]           @default([])
  salary                 String?
  salaryCurrency         String?
  salaryMax              Float?
  salaryMin              Float?
  securityClearance      String?
  skills                 String[]           @default([])
  travelRequired         Boolean            @default(false)
  updatedAt              DateTime           @default(now())
  yearsOfExperience      Int                @default(0)
  experienceRequirements Json?
  companyId              String?
  stateId                String?
  isAnalyzed             Boolean            @default(false)
  companyRelation        company?           @relation(fields: [companyId], references: [id])
  state                  state?             @relation(fields: [stateId], references: [id])
  job_match_result       job_match_result[]

  @@schema("cron")
}

model job_match_result {
  id          String      @id
  userId      String
  jobId       String
  profileId   String
  matchScore  Float
  applied     Boolean     @default(false)
  createdAt   DateTime    @default(now())
  job_listing job_listing @relation(fields: [jobId], references: [id])

  @@unique([userId, jobId, profileId])
  @@schema("cron")
}

model language {
  id        String   @id
  code      String   @unique
  name      String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model occupation {
  id               String             @id
  socCode          String
  title            String
  shortTitle       String?
  category         String
  source           String?
  createdAt        DateTime           @default(now())
  JobMarketMetrics JobMarketMetrics[]
  SkillTrend       SkillTrend[]

  @@unique([socCode, title])
  @@schema("cron")
}

model school {
  id          String   @id
  institution String
  countryId   String?
  stateId     String?
  createdAt   DateTime @default(now())
  country     country? @relation(fields: [countryId], references: [id])
  state       state?   @relation(fields: [stateId], references: [id])

  @@unique([institution, stateId, countryId])
  @@schema("cron")
}

model skill {
  id        String   @id
  name      String   @unique
  type      String?
  source    String
  createdAt DateTime @default(now())

  @@schema("cron")
}

model state {
  id          String        @id
  name        String
  code        String?
  countryId   String
  createdAt   DateTime      @default(now())
  city        city[]
  company     company[]
  job_listing job_listing[]
  school      school[]
  country     country       @relation(fields: [countryId], references: [id], onDelete: Cascade)

  @@unique([countryId, code])
  @@unique([countryId, name])
  @@schema("cron")
}

model scrapeProgress {
  id                  String   @id
  type                String   @unique
  lastCityIndex       Int?
  metadata            String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())
  lastOccupationIndex Int?

  @@index([lastOccupationIndex])
  @@schema("cron")
}

model JobMarketMetrics {
  id           String     @id
  occupationId String
  level        String?
  remoteCount  Int
  totalCount   Int
  avgSalary    Float?
  salaryRange  Json?
  topSkills    Json?
  topCompanies Json?
  collectedAt  DateTime   @default(now())
  occupation   occupation @relation(fields: [occupationId], references: [id])

  @@unique([occupationId, level, collectedAt])
  @@schema("cron")
}

model SkillTrend {
  id              String      @id
  skillName       String
  category        String?
  occupationId    String?
  mentionCount    Int
  growthRate      Float?
  avgSalaryImpact Float?
  collectedAt     DateTime    @default(now())
  occupation      occupation? @relation(fields: [occupationId], references: [id])

  @@unique([skillName, occupationId, collectedAt])
  @@schema("cron")
}

model JobStats {
  id             String    @id
  jobType        String
  itemsProcessed Int       @default(0)
  success        Boolean   @default(false)
  durationMs     Int       @default(0)
  details        Json?
  createdAt      DateTime  @default(now())
  startTime      DateTime  @default(now())
  endTime        DateTime?
  error          String?
  updatedAt      DateTime

  @@index([createdAt])
  @@index([endTime])
  @@index([jobType])
  @@schema("cron")
}

enum CompanySize {
  SIZE_1_10
  SIZE_11_50
  SIZE_51_200
  SIZE_201_500
  SIZE_501_1000
  SIZE_1001_5000
  SIZE_5001_10000
  SIZE_10000_PLUS

  @@schema("cron")
}

enum CompanyStage {
  BOOTSTRAPPED
  PRE_SEED
  SEED
  SERIES_A
  SERIES_B
  SERIES_C
  PUBLIC
  ACQUIRED
  ENTERPRISE

  @@schema("cron")
}
